import 'dart:io';
import 'dart:math';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/utils/app_logger.dart';
import 'package:spot_on/utils/imports.dart';

Future<void> firebaseMessageBackgroundHandler() async {
  JobsState jobsState = globalKey.currentContext!.read<JobsState>();
  CreateJobState cJobState = globalKey.currentContext!.read<CreateJobState>();
  getJobsForHomeTab();
  jobsState.getJobs();
  cJobState.getEntryExitReportList();
}

class FBPushNotification {
  static FirebaseMessaging messaging = FirebaseMessaging.instance;

  static Future<bool> requestPushPermissions() async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    printLog('User granted permission: ${settings.authorizationStatus}');
    return settings.authorizationStatus.index == 0;
  }

  static getToken() async {
    String? token = await messaging.getToken();
    printLog(token!);
    print('>>>>token>>>>>>>>>>>>$token');
    return token;
  }

  static initListeners() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('lastMessageId', '');
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    initializeAwesomeNotifications();
    await Future.delayed(const Duration(seconds: 2));
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      JobsState jobsState = globalKey.currentContext!.read<JobsState>();
      getJobsForHomeTab();
      jobsState.getJobs();
    });
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      print('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
      var lastMessageId = prefs.getString('lastMessageId');
      printLog('Got a message whilst in the foreground!');
      printLog('Message data: ${message.data}');
      if (message.messageId != lastMessageId) {
        JobsState jobsState = globalKey.currentContext!.read<JobsState>();
        getJobsForHomeTab();
        jobsState.getJobs();
        if (message.notification != null) {
          print('>>>>>>>>>>>>>>>>>>>>>>>>>>00<<<<<<<<<<<<<<<<<<<<<<<<<');

          if (Platform.isAndroid) {
            createPush(
              title: message.notification?.title ?? "SpotOn",
              body:
                  message.notification?.body ?? "Hope you are enjoying SpotOn!",
            );
          }
          printLog(
              'Message also contained a notification: ${message.notification}');
        }
      }
      await prefs.setString('lastMessageId', message.messageId ?? '');
    });

    printLog("Push notification listener added");
  }

  static initializeAwesomeNotifications() {
    AwesomeNotifications().initialize(
      'resource://drawable/spot_on_logo',
      [
        NotificationChannel(
          channelGroupKey: 'spot-on-1',
          channelKey: 'spot-on-1',
          channelName: 'spot-on-1',
          channelDescription: 'spot-on-1',
          defaultColor: appColor,
          ledColor: Colors.white,
        )
      ],
      channelGroups: [
        NotificationChannelGroup(
          channelGroupName: 'Basic group',
          channelGroupKey: 'spot-on-1',
        )
      ],
      debug: false,
    );
  }

  static createPush({required String title, required String body}) {
    AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: Random().nextInt(1000),
        channelKey: 'spot-on-1',
        title: title,
        body: body,
      ),
    );
  }
}
